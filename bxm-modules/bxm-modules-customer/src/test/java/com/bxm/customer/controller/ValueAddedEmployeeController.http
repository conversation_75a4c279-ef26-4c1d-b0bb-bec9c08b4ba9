### ValueAddedEmployeeController HTTP测试文件
### 增值员工信息管理接口测试
### 支持四种业务类型：1-社医保，2-个税明细，3-国税账号，4-个税账号
###
### 主要功能：
### 1. 员工信息的增删改查操作
### 2. 批量上传Excel文件处理
### 3. 根据业务类型导出不同的Excel模板
###    - bizType=1: 社医保模板（使用SocialInsuranceDTO）
###      字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育
###    - bizType=2: 个税明细模板（使用PersonalTaxDetailExportDTO）
###      字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育、应发工资、公积金个人缴存金额、其他
###    - bizType=3: 国税账号（手动录入，无Excel模板）
###    - bizType=4: 个税账号（手动录入，无Excel模板）

### 环境变量定义
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjExYTY4MDc2LTE5ZDgtNDdmNy04ZWVhLTQzM2JlNWNhMThiMyIsInVzZXJuYW1lIjoiYWRtaW4ifQ.NCiZg2AaDS9WjdPV1DffXxT5gWWaVzik-Qk2M3qeAS8VEjyW-9XNGIB5erI9NH9BuhW70nzhch2nF8fYehddOQ
### ===========================================
### 1. 社医保业务类型测试 (bizType=1)
### ===========================================

### 1.1 社医保-提醒操作 (operationType=1)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-001",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "张三",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 8000.00,
  "providentFundPersonal": 800.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 1,
  "remark": "社医保提醒操作测试"
}

### 1.2 社医保-更正操作 (operationType=2)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-002",
  "bizType": 1,
  "entryType": 1,
  "operationType": 2,
  "employeeName": "李四",
  "idNumber": "110101199002022345",
  "mobile": "13800138002",
  "grossSalary": 9500.00,
  "providentFundPersonal": 950.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 2,
  "remark": "社医保更正操作测试"
}

### 1.3 社医保-减员操作 (operationType=3)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-003",
  "bizType": 1,
  "entryType": 2,
  "operationType": 3,
  "employeeName": "王五",
  "idNumber": "110101199003033456",
  "mobile": "13800138003",
  "grossSalary": 7500.00,
  "providentFundPersonal": 750.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 3,
  "remark": "社医保减员操作测试"
}

### ===========================================
### 2. 个税明细业务类型测试 (bizType=2)
### ===========================================

### 2.1 个税明细-提醒操作 (operationType=1)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-004",
  "bizType": 2,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "赵六",
  "idNumber": "110101199004044567",
  "mobile": "13800138004",
  "grossSalary": 12000.00,
  "providentFundPersonal": 1200.00,
  "status": 1,
  "extendInfo": "{\"taxableIncome\":\"10800.00\",\"taxAmount\":\"540.00\",\"socialInsuranceBase\":\"10000.00\",\"yangLao\":\"800.00\",\"shiYe\":\"100.00\",\"gongShang\":\"80.00\",\"yiLiao\":\"200.00\",\"shengYu\":\"120.00\"}",
  "remark": "个税明细提醒操作测试（包含社保基数和各险种）"
}

### 2.2 个税明细-更正操作 (operationType=2)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-005",
  "bizType": 2,
  "entryType": 1,
  "operationType": 2,
  "employeeName": "孙七",
  "idNumber": "110101199005055678",
  "mobile": "13800138005",
  "grossSalary": 15000.00,
  "providentFundPersonal": 1500.00,
  "status": 2,
  "extendInfo": "{\"taxableIncome\":\"13500.00\",\"taxAmount\":\"945.00\",\"socialInsuranceBase\":\"12000.00\",\"yangLao\":\"960.00\",\"shiYe\":\"120.00\",\"gongShang\":\"96.00\",\"yiLiao\":\"240.00\",\"shengYu\":\"144.00\"}",
  "remark": "个税明细更正操作测试（包含社保基数和各险种）"
}

### 2.3 个税明细-社保字段完整测试（包含所有险种）
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-006",
  "bizType": 2,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "周八",
  "idNumber": "110101199006066789",
  "mobile": "13800138006",
  "grossSalary": 18000.00,
  "providentFundPersonal": 1800.00,
  "status": 1,
  "extendInfo": "{\"socialInsuranceBase\":\"15000.00\",\"yangLao\":\"1200.00\",\"shiYe\":\"150.00\",\"gongShang\":\"120.00\",\"yiLiao\":\"300.00\",\"shengYu\":\"180.00\",\"taxableIncome\":\"16200.00\",\"taxAmount\":\"1215.00\",\"insuranceTypes\":[\"养老保险\",\"失业保险\",\"工伤保险\",\"医疗保险\",\"生育保险\",\"其他\"]}",
  "remark": "个税明细社保字段完整测试 - 包含社保基数、养老、失业、工伤、医疗、生育、其他险种"
}

### ===========================================
### 3. 国税账号业务类型测试 (bizType=3)
### ===========================================

### 3.1 国税账号-会计实名 (operationType=1)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-006",
  "bizType": 3,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "周八",
  "idNumber": "110101199006066789",
  "mobile": "13800138006",
  "taxNumber": "91110000123456789X",
  "queryPassword": "password123",
  "status": 1,
  "extendInfo": "{\"certificationDate\":\"2025-01-28\",\"customField\":\"会计实名认证\"}",
  "remark": "国税账号会计实名测试"
}

### 3.2 国税账号-异地实名 (operationType=2)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-007",
  "bizType": 3,
  "entryType": 1,
  "operationType": 2,
  "employeeName": "吴九",
  "idNumber": "110101199007077890",
  "mobile": "13800138007",
  "taxNumber": "91110000987654321Y",
  "queryPassword": "password456",
  "status": 1,
  "extendInfo": "{\"remoteLocation\":\"上海\",\"customField\":\"异地实名认证\"}",
  "remark": "国税账号异地实名测试"
}

### ===========================================
### 4. 个税账号业务类型测试 (bizType=4)
### ===========================================

### 4.1 个税账号-个税账号添加 (operationType=1)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-008",
  "bizType": 4,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "郑十",
  "idNumber": "110101199008088901",
  "mobile": "13800138008",
  "taxNumber": "91110000555666777Z",
  "queryPassword": "password789",
  "loginMethod": "手机号+密码",
  "realNameAgent": "张会计",
  "status": 1,
  "remark": "个税账号添加测试"
}

### 4.2 个税账号-验证扩展信息自动生成
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-009",
  "bizType": 4,
  "entryType": 1,
  "operationType": 1,
  "employeeName": "王十一",
  "idNumber": "110101199009099012",
  "mobile": "13800138009",
  "taxNumber": "91110000888999000A",
  "queryPassword": "password000",
  "loginMethod": "身份证号+密码",
  "realNameAgent": "李会计",
  "status": 1,
  "extendInfo": "{\"customField\":\"自定义字段值\"}",
  "remark": "个税账号扩展信息测试"
}

### ===========================================
### 5. 更新操作测试（包含ID字段）
### ===========================================

### 4.1 更新已存在的员工信息
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "id": 1,
  "deliveryOrderNo": "DO-2025-001",
  "bizType": 1,
  "entryType": 2,
  "operationType": 2,
  "employeeName": "张三（已更新）",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 8500.00,
  "providentFundPersonal": 850.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 2,
  "remark": "更新操作测试"
}

### ===========================================
### 5. 参数验证失败测试
### ===========================================

### 5.1 缺少必填字段测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "测试用户"
}

### 5.2 身份证号格式错误测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-901",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "格式错误测试",
  "idNumber": "123456789",
  "mobile": "13800138001",
  "grossSalary": 8000.00
}

### 5.3 手机号格式错误测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-902",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "手机号错误测试",
  "idNumber": "110101199001011234",
  "mobile": "12345678901",
  "grossSalary": 8000.00
}

### 5.4 业务类型超出范围测试 (bizType=5)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-903",
  "bizType": 5,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "业务类型错误测试",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 8000.00
}

### 5.5 个税账号业务类型缺少必填字段测试 - 缺少税号
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-904",
  "bizType": 4,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "个税账号缺少税号测试",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "queryPassword": "password123",
  "loginMethod": "手机号+密码",
  "realNameAgent": "测试会计"
}

### 5.6 个税账号业务类型缺少必填字段测试 - 缺少实名经办人
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-905",
  "bizType": 4,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "个税账号缺少实名经办人测试",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "taxNumber": "91110000123456789X",
  "queryPassword": "password123",
  "loginMethod": "手机号+密码"
}

### 5.7 个税账号操作类型错误测试 (operationType=2)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-906",
  "bizType": 4,
  "entryType": 2,
  "operationType": 2,
  "employeeName": "个税账号操作类型错误测试",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "taxNumber": "91110000123456789X",
  "queryPassword": "password123",
  "loginMethod": "手机号+密码",
  "realNameAgent": "测试会计"
}

### ===========================================
### 6. 扩展信息验证专项测试
### ===========================================

### 6.1 验证策略优化后的扩展信息自动生成 - 社医保
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-101",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "扩展信息测试-社医保",
  "idNumber": "110101199101011234",
  "mobile": "13800138101",
  "grossSalary": 8000.00,
  "status": 1,
  "remark": "验证社医保扩展信息自动生成"
}

### 6.2 验证策略优化后的扩展信息自动生成 - 个税明细
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-102",
  "bizType": 2,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "扩展信息测试-个税明细",
  "idNumber": "110101199102022345",
  "mobile": "13800138102",
  "grossSalary": 12000.00,
  "status": 1,
  "extendInfo": "{\"socialInsuranceBase\":\"11000.00\",\"yangLao\":\"880.00\",\"shiYe\":\"110.00\",\"gongShang\":\"88.00\",\"yiLiao\":\"220.00\",\"shengYu\":\"132.00\",\"customField\":\"个税明细扩展信息测试\"}",
  "remark": "验证个税明细扩展信息自动生成（包含社保基数和各险种）"
}

### 6.3 验证策略优化后的扩展信息自动生成 - 国税账号
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-103",
  "bizType": 3,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "扩展信息测试-国税账号",
  "idNumber": "110101199103033456",
  "mobile": "13800138103",
  "taxNumber": "91110000111222333B",
  "queryPassword": "password111",
  "status": 1,
  "remark": "验证国税账号扩展信息自动生成"
}

### 6.4 验证策略优化后的扩展信息自动生成 - 个税账号
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-104",
  "bizType": 4,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "扩展信息测试-个税账号",
  "idNumber": "110101199104044567",
  "mobile": "13800138104",
  "taxNumber": "91110000444555666C",
  "queryPassword": "password222",
  "loginMethod": "手机号+验证码",
  "realNameAgent": "测试会计",
  "status": 1,
  "remark": "验证个税账号扩展信息自动生成"
}

### ===========================================
### 7. 查询接口测试（辅助验证）
### ===========================================

### 6.1 根据交付单编号和身份证号查询员工信息
GET {{baseUrl}}/valueAddedEmployee/getByDeliveryOrderAndIdNumber?deliveryOrderNo=DO-2025-001&idNumber=110101199001011234&bizType=1

### 6.2 根据ID查询员工详情
GET {{baseUrl}}/valueAddedEmployee/getById/1

### ===========================================
### 7. 边界值测试
### ===========================================

### 7.1 最大长度字段测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-801",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "这是一个非常长的员工姓名用来测试最大长度限制这是一个非常长的员工姓名用来测试最大长度限制这是一个非常长的员工姓名用来测试最大长度限制",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 99999999.99,
  "providentFundPersonal": 99999999.99,
  "remark": "这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制"
}


### 7.2 最小值测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-802",
  "bizType": 1,
  "entryType": 1,
  "operationType": 1,
  "employeeName": "最小值测试",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 0.00,
  "providentFundPersonal": 0.00,
  "status": 1
}


### ===========================================
### 8. Excel模板导出测试 (exportValueAddedEmptyExcelTemplate)
### ===========================================
###
### 接口说明：
### - 路径：GET /valueAddedEmployee/exportValueAddedEmptyExcelTemplate
### - 参数：bizType (必填) - 业务类型：1-社医保，2-个税明细
### - 功能：根据业务类型下载对应的空白Excel模板
### - 返回：Excel文件下载
###
### 业务类型说明：
### - bizType=1: 使用SocialInsuranceDTO生成社保明细模板
###   包含字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育
### - bizType=2: 使用PersonalTaxDetailExportDTO生成个税明细模板
###   包含字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育、应发工资、公积金个人缴存金额、其他
###

### 8.1 导出社保明细Excel模板 (bizType=1) - 使用SocialInsuranceDTO
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=1
Authorization: {{authorization}}

### 8.2 导出个税明细Excel模板 (bizType=2) - 使用PersonalTaxDetailExportDTO
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=2
Authorization: {{authorization}}

### 8.3 导出模板参数验证失败测试 - 缺少bizType参数
### 预期结果：400 Bad Request - 缺少必需的请求参数
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate
Authorization: {{authorization}}

### 8.4 导出模板参数验证失败测试 - bizType参数无效 (bizType=3)
### 预期结果：400 Bad Request - 业务类型参数无效，必须为1（社医保）或2（个税明细）
### 注意：bizType=3（国税账号）和bizType=4（个税账号）不支持Excel模板导出
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=3
Authorization: {{authorization}}

### 8.5 导出模板参数验证失败测试 - bizType参数无效 (bizType=4)
### 预期结果：400 Bad Request - 业务类型参数无效，必须为1（社医保）或2（个税明细）
### 注意：bizType=4（个税账号）不支持Excel模板导出
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=4
Authorization: {{authorization}}

### 8.6 导出模板参数验证失败测试 - bizType参数无效 (bizType=0)
### 预期结果：400 Bad Request - 业务类型参数无效，必须为1（社医保）或2（个税明细）
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=0
Authorization: {{authorization}}

### 8.7 导出模板参数验证失败测试 - bizType参数无效 (bizType=abc)
### 预期结果：400 Bad Request - 参数类型转换错误
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=abc
Authorization: {{authorization}}

### 8.8 导出模板参数验证失败测试 - bizType参数为负数 (bizType=-1)
### 预期结果：400 Bad Request - 业务类型参数无效，必须为1（社医保）或2（个税明细）
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=-1
Authorization: {{authorization}}

### ===========================================
### 9. 批量上传接口测试 (batchAddSocialInsuranceEmp)
### ===========================================

### 9.1 社医保批量上传测试 (bizType=1) - 使用现有Excel文件
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-001
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

社医保批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 9.2 个税明细批量上传测试 (bizType=2)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-004
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

2
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

个税明细批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 9.3 国税账号批量上传测试 (bizType=3)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-006
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

3
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

国税账号批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 9.4 个税账号批量上传测试 (bizType=4)
### 注意：个税账号业务类型不支持Excel批量上传，此测试应返回错误
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-008
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

4
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

个税账号批量上传测试（应失败）
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 9.5 覆盖现有数据测试 (overrideExisting=true)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-002
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

覆盖现有数据测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

true
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ===========================================
### 10. 批量上传参数验证失败测试
### ===========================================

### 10.1 缺少Excel文件测试
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-901
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

缺少文件测试
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 10.2 业务类型超出范围测试 (bizType=5)
### 预期结果：400 Bad Request - 业务类型参数无效
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-902
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

5
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

业务类型错误测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 10.3 业务类型为0测试 (bizType=0)
### 预期结果：400 Bad Request - 业务类型参数无效
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-903
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

0
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

业务类型为0测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ===========================================
### 11. 文件处理状态查询测试
### ===========================================

### 11.1 查询文件处理状态 (需要先执行上面的批量上传获取fileId)
### 注意：请将下面的fileId替换为实际的文件ID
GET {{baseUrl}}/valueAddedEmployee/fileProgress/16
Authorization: {{authorization}}

### 11.2 查询不存在的文件状态
GET {{baseUrl}}/valueAddedEmployee/fileProgress/99999
Authorization: {{authorization}}

